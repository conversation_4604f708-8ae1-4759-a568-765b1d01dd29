import React, { useState, useEffect } from 'react';
import { calculateZoomScale, ICursorPosition } from '../features/editor/utils/zoom-calculations';
import { IZoomConfig } from '../features/editor/store/use-zoom-store';

/**
 * Demo component to test cursor following zoom functionality
 * This can be used to verify the implementation works correctly
 */

interface CursorFollowingDemoProps {
  canvasWidth?: number;
  canvasHeight?: number;
}

export const CursorFollowingDemo: React.FC<CursorFollowingDemoProps> = ({
  canvasWidth = 1920,
  canvasHeight = 1080
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [cursorData, setCursorData] = useState<ICursorPosition[]>([]);
  const [zoomResult, setZoomResult] = useState<any>(null);

  // Generate demo cursor data (circular pattern)
  useEffect(() => {
    const generateDemoCursorData = () => {
      const data: ICursorPosition[] = [];
      const centerX = canvasWidth / 2;
      const centerY = canvasHeight / 2;
      const radius = Math.min(canvasWidth, canvasHeight) * 0.3;
      const duration = 10000; // 10 seconds
      const fps = 30;
      const totalFrames = (duration / 1000) * fps;

      for (let frame = 0; frame < totalFrames; frame++) {
        const time = (frame / fps) * 1000;
        const angle = (frame / totalFrames) * 2 * Math.PI;
        
        const x = centerX + Math.cos(angle) * radius;
        const y = centerY + Math.sin(angle) * radius;
        
        data.push({
          x,
          y,
          frame,
          time,
          action: 'move'
        });
      }
      
      return data;
    };

    setCursorData(generateDemoCursorData());
  }, [canvasWidth, canvasHeight]);

  // Animation loop
  useEffect(() => {
    let animationFrame: number;
    
    if (isPlaying) {
      const animate = () => {
        setCurrentTime(prev => {
          const next = prev + 16.67; // ~60fps
          return next > 10000 ? 0 : next; // Loop after 10 seconds
        });
        animationFrame = requestAnimationFrame(animate);
      };
      animationFrame = requestAnimationFrame(animate);
    }
    
    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isPlaying]);

  // Calculate zoom result for current time
  useEffect(() => {
    if (cursorData.length === 0) return;

    const zoomConfig: IZoomConfig = {
      maxZoomScale: 2.5,
      zoomOut: { enabled: false, duration: 500 },
      bezierControlPoints: { p1: 0.25, p2: 0.1, p3: 0.25, p4: 1 },
      position: { x: 0.5, y: 0.5 },
      zoomArea: { x: 0.25, y: 0.25, width: 0.5, height: 0.5 },
      cursorFollowing: {
        defaultZoomRadius: 0.15,
        defaultSmoothingFactor: 0.7,
        defaultEdgePadding: 0.1,
        defaultMinZoomScale: 1.5,
        defaultMaxZoomScale: 3.0,
        enabledByDefault: true,
      }
    };

    const zoomEffect = {
      id: 'demo-cursor-following',
      startTime: 0,
      endTime: 10000,
      cursorFollowing: {
        enabled: true,
        zoomRadius: 0.15,
        smoothingFactor: 0.7,
        edgePadding: 0.1,
        minZoomScale: 1.5,
        maxZoomScale: 3.0,
      }
    };

    const result = calculateZoomScale({
      currentPosition: currentTime,
      isFrameBased: false,
      zoomTiming: { startTime: 0, endTime: 10000 },
      zoomConfig,
      zoomEffect,
      cursorData,
      canvasDimensions: { width: canvasWidth, height: canvasHeight },
    });

    setZoomResult(result);
  }, [currentTime, cursorData, canvasWidth, canvasHeight]);

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
  };

  const resetDemo = () => {
    setCurrentTime(0);
    setIsPlaying(false);
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>🎯 Cursor Following Zoom Demo</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={togglePlayback}
          style={{ 
            padding: '10px 20px', 
            marginRight: '10px',
            backgroundColor: isPlaying ? '#ff4444' : '#44ff44',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          {isPlaying ? '⏸️ Pause' : '▶️ Play'}
        </button>
        
        <button 
          onClick={resetDemo}
          style={{ 
            padding: '10px 20px',
            backgroundColor: '#4444ff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          🔄 Reset
        </button>
      </div>

      <div style={{ display: 'flex', gap: '20px' }}>
        {/* Canvas Preview */}
        <div style={{ flex: 1 }}>
          <h3>Canvas Preview</h3>
          <div 
            style={{
              width: '400px',
              height: '225px', // 16:9 aspect ratio
              border: '2px solid #ccc',
              position: 'relative',
              backgroundColor: '#f0f0f0',
              overflow: 'hidden'
            }}
          >
            {/* Cursor position indicator */}
            {zoomResult?.currentCursorPosition && (
              <div
                style={{
                  position: 'absolute',
                  left: `${(zoomResult.currentCursorPosition.x / canvasWidth) * 100}%`,
                  top: `${(zoomResult.currentCursorPosition.y / canvasHeight) * 100}%`,
                  width: '10px',
                  height: '10px',
                  backgroundColor: 'red',
                  borderRadius: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 2
                }}
              />
            )}
            
            {/* Zoom area indicator */}
            {zoomResult?.dynamicZoomArea && (
              <div
                style={{
                  position: 'absolute',
                  left: `${zoomResult.dynamicZoomArea.x * 100}%`,
                  top: `${zoomResult.dynamicZoomArea.y * 100}%`,
                  width: `${zoomResult.dynamicZoomArea.width * 100}%`,
                  height: `${zoomResult.dynamicZoomArea.height * 100}%`,
                  border: '2px solid blue',
                  backgroundColor: 'rgba(0, 0, 255, 0.1)',
                  zIndex: 1
                }}
              />
            )}
            
            {/* Canvas content simulation */}
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: `translate(-50%, -50%) scale(${zoomResult?.zoomScale || 1})`,
              transformOrigin: zoomResult?.dynamicZoomArea 
                ? `${(zoomResult.dynamicZoomArea.x + zoomResult.dynamicZoomArea.width / 2) * 100}% ${(zoomResult.dynamicZoomArea.y + zoomResult.dynamicZoomArea.height / 2) * 100}%`
                : '50% 50%',
              width: '100%',
              height: '100%',
              backgroundColor: 'rgba(0, 255, 0, 0.1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '14px',
              color: '#666'
            }}>
              Video Content
            </div>
          </div>
        </div>

        {/* Debug Information */}
        <div style={{ flex: 1 }}>
          <h3>Debug Information</h3>
          <div style={{ 
            backgroundColor: '#f8f8f8', 
            padding: '15px', 
            borderRadius: '5px',
            fontFamily: 'monospace',
            fontSize: '12px'
          }}>
            <div><strong>Time:</strong> {currentTime.toFixed(0)}ms</div>
            <div><strong>Playing:</strong> {isPlaying ? 'Yes' : 'No'}</div>
            <div><strong>Cursor Data Points:</strong> {cursorData.length}</div>
            
            {zoomResult && (
              <>
                <hr style={{ margin: '10px 0' }} />
                <div><strong>Zoom Active:</strong> {zoomResult.isZoomActive ? 'Yes' : 'No'}</div>
                <div><strong>Zoom Scale:</strong> {zoomResult.zoomScale.toFixed(2)}x</div>
                <div><strong>Progress:</strong> {(zoomResult.progress * 100).toFixed(1)}%</div>
                <div><strong>Phase:</strong> {zoomResult.phase}</div>
                <div><strong>Cursor Following:</strong> {zoomResult.isCursorFollowing ? 'Yes' : 'No'}</div>
                
                {zoomResult.currentCursorPosition && (
                  <>
                    <hr style={{ margin: '10px 0' }} />
                    <div><strong>Cursor Position:</strong></div>
                    <div>  X: {Math.round(zoomResult.currentCursorPosition.x)}</div>
                    <div>  Y: {Math.round(zoomResult.currentCursorPosition.y)}</div>
                  </>
                )}
                
                {zoomResult.dynamicZoomArea && (
                  <>
                    <hr style={{ margin: '10px 0' }} />
                    <div><strong>Dynamic Zoom Area:</strong></div>
                    <div>  X: {zoomResult.dynamicZoomArea.x.toFixed(3)}</div>
                    <div>  Y: {zoomResult.dynamicZoomArea.y.toFixed(3)}</div>
                    <div>  W: {zoomResult.dynamicZoomArea.width.toFixed(3)}</div>
                    <div>  H: {zoomResult.dynamicZoomArea.height.toFixed(3)}</div>
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3>Test Status</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <span style={{ 
            padding: '5px 10px', 
            backgroundColor: zoomResult?.isCursorFollowing ? '#4CAF50' : '#f44336',
            color: 'white',
            borderRadius: '3px',
            fontSize: '12px'
          }}>
            Cursor Following: {zoomResult?.isCursorFollowing ? 'Active' : 'Inactive'}
          </span>
          
          <span style={{ 
            padding: '5px 10px', 
            backgroundColor: zoomResult?.currentCursorPosition ? '#4CAF50' : '#f44336',
            color: 'white',
            borderRadius: '3px',
            fontSize: '12px'
          }}>
            Cursor Data: {zoomResult?.currentCursorPosition ? 'Available' : 'Missing'}
          </span>
          
          <span style={{ 
            padding: '5px 10px', 
            backgroundColor: zoomResult?.dynamicZoomArea ? '#4CAF50' : '#f44336',
            color: 'white',
            borderRadius: '3px',
            fontSize: '12px'
          }}>
            Dynamic Zoom: {zoomResult?.dynamicZoomArea ? 'Working' : 'Not Working'}
          </span>
        </div>
      </div>
    </div>
  );
};
