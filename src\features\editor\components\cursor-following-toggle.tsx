import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { CursorFollowingControls } from './cursor-following-controls';
import { IZoomEffect } from '../store/use-store';

interface CursorFollowingToggleProps {
  /** The zoom effect to configure */
  zoomEffect?: IZoomEffect;
  /** Callback when cursor following configuration changes */
  onConfigChange?: (config: IZoomEffect['cursorFollowing']) => void;
  /** Whether the toggle is disabled */
  disabled?: boolean;
  /** Show as a simple switch without popover */
  simple?: boolean;
  /** Custom label text */
  label?: string;
}

/**
 * Cursor Following Toggle Component
 * 
 * A compact toggle control for cursor following that can be embedded in
 * zoom effect panels, timeline overlays, and other UI components.
 * Includes a popover with detailed configuration options.
 */
export const CursorFollowingToggle: React.FC<CursorFollowingToggleProps> = ({
  zoomEffect,
  onConfigChange,
  disabled = false,
  simple = false,
  label = "Cursor Following"
}) => {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  // Get current cursor following state
  const isEnabled = zoomEffect?.cursorFollowing?.enabled || false;
  const hasCustomConfig = zoomEffect?.cursorFollowing && (
    zoomEffect.cursorFollowing.zoomRadius !== undefined ||
    zoomEffect.cursorFollowing.smoothingFactor !== undefined ||
    zoomEffect.cursorFollowing.edgePadding !== undefined ||
    zoomEffect.cursorFollowing.minZoomScale !== undefined ||
    zoomEffect.cursorFollowing.maxZoomScale !== undefined
  );

  const handleToggle = (enabled: boolean) => {
    const newConfig = {
      ...zoomEffect?.cursorFollowing,
      enabled
    };
    onConfigChange?.(newConfig);
  };

  const handleConfigChange = (config: IZoomEffect['cursorFollowing']) => {
    onConfigChange?.(config);
    // Close popover after configuration change
    setIsPopoverOpen(false);
  };

  if (simple) {
    return (
      <div className="flex items-center space-x-2">
        <Switch
          checked={isEnabled}
          onCheckedChange={handleToggle}
          disabled={disabled}
        />
        <span className="text-sm">{label}</span>
        {isEnabled && hasCustomConfig && (
          <Badge variant="secondary" className="text-xs">
            Custom
          </Badge>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      {/* Main Toggle */}
      <Switch
        checked={isEnabled}
        onCheckedChange={handleToggle}
        disabled={disabled}
      />
      
      {/* Label and Status */}
      <div className="flex items-center space-x-2">
        <span className="text-sm font-medium">🎯 {label}</span>
        
        {/* Status Badges */}
        {isEnabled && (
          <>
            <Badge variant="default" className="text-xs bg-green-100 text-green-800">
              Active
            </Badge>
            {hasCustomConfig && (
              <Badge variant="secondary" className="text-xs">
                Custom Config
              </Badge>
            )}
          </>
        )}
        
        {!isEnabled && (
          <Badge variant="outline" className="text-xs">
            Disabled
          </Badge>
        )}
      </div>

      {/* Configuration Popover */}
      {isEnabled && (
        <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              disabled={disabled}
              className="h-6 w-6 p-0"
            >
              ⚙️
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="start">
            <CursorFollowingControls
              zoomEffect={zoomEffect}
              onConfigChange={handleConfigChange}
              compact={true}
            />
          </PopoverContent>
        </Popover>
      )}
    </div>
  );
};

/**
 * Cursor Following Status Indicator
 * 
 * A read-only indicator showing the current cursor following status
 * for use in timeline overlays and other display contexts.
 */
interface CursorFollowingStatusProps {
  /** The zoom effect to display status for */
  zoomEffect?: IZoomEffect;
  /** Show detailed configuration info */
  detailed?: boolean;
}

export const CursorFollowingStatus: React.FC<CursorFollowingStatusProps> = ({
  zoomEffect,
  detailed = false
}) => {
  const isEnabled = zoomEffect?.cursorFollowing?.enabled || false;
  const config = zoomEffect?.cursorFollowing;

  if (!isEnabled) {
    return (
      <Badge variant="outline" className="text-xs">
        Standard Zoom
      </Badge>
    );
  }

  if (!detailed) {
    return (
      <Badge variant="default" className="text-xs bg-blue-100 text-blue-800">
        🎯 Cursor Following
      </Badge>
    );
  }

  return (
    <div className="flex flex-col space-y-1">
      <Badge variant="default" className="text-xs bg-blue-100 text-blue-800">
        🎯 Cursor Following Active
      </Badge>
      {config && (
        <div className="text-xs text-muted-foreground">
          <div>Radius: {(config.zoomRadius || 0.15) * 100}%</div>
          <div>Smoothing: {(config.smoothingFactor || 0.7) * 100}%</div>
          <div>Scale: {config.minZoomScale || 1.5}x - {config.maxZoomScale || 3.0}x</div>
        </div>
      )}
    </div>
  );
};

/**
 * Cursor Following Quick Actions
 * 
 * Quick action buttons for common cursor following operations
 */
interface CursorFollowingQuickActionsProps {
  /** The zoom effect to configure */
  zoomEffect?: IZoomEffect;
  /** Callback when configuration changes */
  onConfigChange?: (config: IZoomEffect['cursorFollowing']) => void;
  /** Whether actions are disabled */
  disabled?: boolean;
}

export const CursorFollowingQuickActions: React.FC<CursorFollowingQuickActionsProps> = ({
  zoomEffect,
  onConfigChange,
  disabled = false
}) => {
  const isEnabled = zoomEffect?.cursorFollowing?.enabled || false;

  const handlePreset = (preset: 'gentle' | 'responsive' | 'precise') => {
    let config;
    
    switch (preset) {
      case 'gentle':
        config = {
          enabled: true,
          zoomRadius: 0.2,
          smoothingFactor: 0.9,
          edgePadding: 0.15,
          minZoomScale: 1.3,
          maxZoomScale: 2.5,
        };
        break;
      case 'responsive':
        config = {
          enabled: true,
          zoomRadius: 0.15,
          smoothingFactor: 0.7,
          edgePadding: 0.1,
          minZoomScale: 1.5,
          maxZoomScale: 3.0,
        };
        break;
      case 'precise':
        config = {
          enabled: true,
          zoomRadius: 0.1,
          smoothingFactor: 0.5,
          edgePadding: 0.05,
          minZoomScale: 2.0,
          maxZoomScale: 4.0,
        };
        break;
    }
    
    onConfigChange?.(config);
  };

  return (
    <div className="flex flex-wrap gap-2">
      <Button
        variant={isEnabled ? "secondary" : "default"}
        size="sm"
        onClick={() => handlePreset('gentle')}
        disabled={disabled}
        className="text-xs"
      >
        🌊 Gentle
      </Button>
      <Button
        variant={isEnabled ? "secondary" : "default"}
        size="sm"
        onClick={() => handlePreset('responsive')}
        disabled={disabled}
        className="text-xs"
      >
        ⚡ Responsive
      </Button>
      <Button
        variant={isEnabled ? "secondary" : "default"}
        size="sm"
        onClick={() => handlePreset('precise')}
        disabled={disabled}
        className="text-xs"
      >
        🎯 Precise
      </Button>
    </div>
  );
};
