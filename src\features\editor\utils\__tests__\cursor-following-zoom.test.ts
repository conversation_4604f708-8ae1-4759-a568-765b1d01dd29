import {
  getCursorPositionAtTime,
  calculateCursorZoomArea,
  smoothZoomArea,
  calculateCursorFollowingZoom,
  ICursorPosition,
  ICursorFollowingZoomParams
} from '../zoom-calculations';

describe('Cursor Following Zoom Utilities', () => {
  // Mock cursor data for testing
  const mockCursorData: ICursorPosition[] = [
    { x: 100, y: 100, frame: 0, time: 0, action: 'move' },
    { x: 200, y: 150, frame: 30, time: 1000, action: 'move' },
    { x: 300, y: 200, frame: 60, time: 2000, action: 'move' },
    { x: 400, y: 250, frame: 90, time: 3000, action: 'move' },
  ];

  describe('getCursorPositionAtTime', () => {
    it('should return exact cursor position when time matches', () => {
      const result = getCursorPositionAtTime(mockCursorData, 1000);
      expect(result).toEqual({ x: 200, y: 150, frame: 30, time: 1000, action: 'move' });
    });

    it('should interpolate between cursor positions', () => {
      const result = getCursorPositionAtTime(mockCursorData, 500);
      expect(result?.x).toBe(150); // Midpoint between 100 and 200
      expect(result?.y).toBe(125); // Midpoint between 100 and 150
      expect(result?.time).toBe(500);
    });

    it('should return first position for time before first cursor point', () => {
      const result = getCursorPositionAtTime(mockCursorData, -500);
      expect(result).toEqual({ x: 100, y: 100, frame: 0, time: 0, action: 'move' });
    });

    it('should return last position for time after last cursor point', () => {
      const result = getCursorPositionAtTime(mockCursorData, 5000);
      expect(result).toEqual({ x: 400, y: 250, frame: 90, time: 3000, action: 'move' });
    });

    it('should return null for empty cursor data', () => {
      const result = getCursorPositionAtTime([], 1000);
      expect(result).toBeNull();
    });
  });

  describe('calculateCursorZoomArea', () => {
    const canvasWidth = 1920;
    const canvasHeight = 1080;
    const cursorConfig = {
      zoomRadius: 0.15,
      smoothingFactor: 0.7,
      edgePadding: 0.1,
      minZoomScale: 1.5,
      maxZoomScale: 3.0,
    };

    it('should calculate zoom area centered on cursor', () => {
      const result = calculateCursorZoomArea(960, 540, canvasWidth, canvasHeight, cursorConfig);
      
      // Should be centered on cursor (0.5, 0.5 normalized)
      expect(result.x).toBeCloseTo(0.35); // 0.5 - 0.15
      expect(result.y).toBeCloseTo(0.35); // 0.5 - 0.15
      expect(result.width).toBe(0.3); // 2 * 0.15
      expect(result.height).toBe(0.3); // 2 * 0.15
    });

    it('should constrain zoom area to stay within bounds', () => {
      // Cursor near top-left corner
      const result = calculateCursorZoomArea(50, 50, canvasWidth, canvasHeight, cursorConfig);
      
      // Should be constrained by edge padding
      expect(result.x).toBeGreaterThanOrEqual(cursorConfig.edgePadding);
      expect(result.y).toBeGreaterThanOrEqual(cursorConfig.edgePadding);
    });

    it('should constrain zoom area near bottom-right corner', () => {
      // Cursor near bottom-right corner
      const result = calculateCursorZoomArea(1870, 1030, canvasWidth, canvasHeight, cursorConfig);
      
      // Should be constrained to not exceed bounds
      expect(result.x + result.width).toBeLessThanOrEqual(1 - cursorConfig.edgePadding);
      expect(result.y + result.height).toBeLessThanOrEqual(1 - cursorConfig.edgePadding);
    });
  });

  describe('smoothZoomArea', () => {
    const currentArea = { x: 0.4, y: 0.4, width: 0.3, height: 0.3 };
    const previousArea = { x: 0.2, y: 0.2, width: 0.3, height: 0.3 };
    const smoothingFactor = 0.7;

    it('should smooth transition between zoom areas', () => {
      const result = smoothZoomArea(currentArea, previousArea, smoothingFactor);
      
      // Should be between previous and current positions
      expect(result.x).toBeGreaterThan(previousArea.x);
      expect(result.x).toBeLessThan(currentArea.x);
      expect(result.y).toBeGreaterThan(previousArea.y);
      expect(result.y).toBeLessThan(currentArea.y);
    });

    it('should return current area when no previous area', () => {
      const result = smoothZoomArea(currentArea, undefined, smoothingFactor);
      expect(result).toEqual(currentArea);
    });

    it('should apply smoothing factor correctly', () => {
      const result = smoothZoomArea(currentArea, previousArea, 0.5);
      
      // With 0.5 smoothing, should be exactly halfway
      expect(result.x).toBeCloseTo(0.3); // (0.2 + 0.4) / 2
      expect(result.y).toBeCloseTo(0.3); // (0.2 + 0.4) / 2
    });
  });

  describe('calculateCursorFollowingZoom', () => {
    const params: ICursorFollowingZoomParams = {
      currentTime: 1500,
      cursorData: mockCursorData,
      canvasWidth: 1920,
      canvasHeight: 1080,
      cursorConfig: {
        zoomRadius: 0.15,
        smoothingFactor: 0.7,
        edgePadding: 0.1,
        minZoomScale: 1.5,
        maxZoomScale: 3.0,
      },
    };

    it('should calculate cursor following zoom result', () => {
      const result = calculateCursorFollowingZoom(params);
      
      expect(result.zoomScale).toBeGreaterThanOrEqual(params.cursorConfig.minZoomScale);
      expect(result.zoomScale).toBeLessThanOrEqual(params.cursorConfig.maxZoomScale);
      expect(result.zoomArea).toBeDefined();
      expect(result.currentCursorPosition).toBeDefined();
    });

    it('should handle missing cursor data gracefully', () => {
      const paramsWithoutCursor = { ...params, cursorData: [] };
      const result = calculateCursorFollowingZoom(paramsWithoutCursor);
      
      expect(result.zoomScale).toBe(1);
      expect(result.currentCursorPosition).toBeNull();
    });

    it('should apply smoothing when previous zoom area provided', () => {
      const previousZoomArea = { x: 0.1, y: 0.1, width: 0.3, height: 0.3 };
      const paramsWithPrevious = { ...params, previousZoomArea };
      
      const result = calculateCursorFollowingZoom(paramsWithPrevious);
      
      // Result should be smoothed between previous and calculated area
      expect(result.zoomArea).toBeDefined();
    });
  });
});
