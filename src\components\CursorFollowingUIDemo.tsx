import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ZoomConfigPanel } from '../features/editor/components/zoom-config-panel';
import { CursorFollowingControls } from '../features/editor/components/cursor-following-controls';
import { 
  CursorFollowingToggle, 
  CursorFollowingStatus, 
  CursorFollowingQuickActions 
} from '../features/editor/components/cursor-following-toggle';
import { IZoomEffect } from '../features/editor/store/use-store';

/**
 * Comprehensive demo showcasing all cursor following UI components
 * This demonstrates how the cursor following controls integrate with the video editor
 */
export const CursorFollowingUIDemo: React.FC = () => {
  const [demoZoomEffect, setDemoZoomEffect] = useState<IZoomEffect>({
    id: 'demo-cursor-following-effect',
    startTime: 2000,
    endTime: 8000,
    maxZoomScale: 2.5,
    zoomArea: { x: 0.25, y: 0.25, width: 0.5, height: 0.5 },
    cursorFollowing: {
      enabled: false,
      zoomRadius: 0.15,
      smoothingFactor: 0.7,
      edgePadding: 0.1,
      minZoomScale: 1.5,
      maxZoomScale: 3.0,
    }
  });

  const handleCursorFollowingChange = (config: IZoomEffect['cursorFollowing']) => {
    setDemoZoomEffect(prev => ({
      ...prev,
      cursorFollowing: config
    }));
  };

  const resetDemo = () => {
    setDemoZoomEffect({
      id: 'demo-cursor-following-effect',
      startTime: 2000,
      endTime: 8000,
      maxZoomScale: 2.5,
      zoomArea: { x: 0.25, y: 0.25, width: 0.5, height: 0.5 },
      cursorFollowing: {
        enabled: false,
        zoomRadius: 0.15,
        smoothingFactor: 0.7,
        edgePadding: 0.1,
        minZoomScale: 1.5,
        maxZoomScale: 3.0,
      }
    });
  };

  return (
    <div className="p-6 space-y-6 max-w-6xl mx-auto">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">🎯 Cursor Following UI Demo</h1>
        <p className="text-muted-foreground">
          Interactive demonstration of all cursor following UI components
        </p>
      </div>

      <div className="flex justify-center">
        <Button onClick={resetDemo} variant="outline">
          🔄 Reset Demo
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Global Configuration Panel */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Global Configuration</h2>
          <ZoomConfigPanel />
        </div>

        {/* Effect-Specific Controls */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Effect-Specific Controls</h2>
          
          {/* Full Controls */}
          <CursorFollowingControls
            zoomEffect={demoZoomEffect}
            onConfigChange={handleCursorFollowingChange}
          />
        </div>
      </div>

      <Separator />

      {/* Compact Controls Demo */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Compact Controls</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Compact Control Panel</CardTitle>
              <CardDescription>Space-efficient version for inline use</CardDescription>
            </CardHeader>
            <CardContent>
              <CursorFollowingControls
                zoomEffect={demoZoomEffect}
                onConfigChange={handleCursorFollowingChange}
                compact={true}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Toggle with Popover</CardTitle>
              <CardDescription>Quick toggle with configuration popup</CardDescription>
            </CardHeader>
            <CardContent>
              <CursorFollowingToggle
                zoomEffect={demoZoomEffect}
                onConfigChange={handleCursorFollowingChange}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      <Separator />

      {/* Status and Quick Actions */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Status Indicators & Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Simple Toggle</CardTitle>
              <CardDescription>Basic on/off switch</CardDescription>
            </CardHeader>
            <CardContent>
              <CursorFollowingToggle
                zoomEffect={demoZoomEffect}
                onConfigChange={handleCursorFollowingChange}
                simple={true}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Status Display</CardTitle>
              <CardDescription>Read-only status indicator</CardDescription>
            </CardHeader>
            <CardContent>
              <CursorFollowingStatus
                zoomEffect={demoZoomEffect}
                detailed={true}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Presets</CardTitle>
              <CardDescription>One-click configuration presets</CardDescription>
            </CardHeader>
            <CardContent>
              <CursorFollowingQuickActions
                zoomEffect={demoZoomEffect}
                onConfigChange={handleCursorFollowingChange}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      <Separator />

      {/* Timeline Integration Preview */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Timeline Integration Preview</h2>
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Zoom Effect Timeline Bar</CardTitle>
            <CardDescription>How cursor following appears in the timeline</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {/* Mock Timeline Bar */}
              <div className="relative h-12 bg-gray-100 rounded-md overflow-hidden">
                {/* Timeline Background */}
                <div className="absolute inset-0 bg-gradient-to-r from-gray-200 to-gray-300"></div>
                
                {/* Zoom Effect Bar */}
                <div 
                  className="absolute h-full bg-blue-500 rounded-sm flex items-center justify-center"
                  style={{
                    left: '20%',
                    width: '40%'
                  }}
                >
                  <span className="text-white text-xs font-medium">Zoom Effect</span>
                </div>
                
                {/* Cursor Following Indicator */}
                {demoZoomEffect.cursorFollowing?.enabled && (
                  <div 
                    className="absolute top-0 h-2 bg-green-400 rounded-sm"
                    style={{
                      left: '20%',
                      width: '40%'
                    }}
                  >
                  </div>
                )}
              </div>
              
              {/* Timeline Controls */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium">Effect: 2.0s - 8.0s</span>
                  <CursorFollowingStatus zoomEffect={demoZoomEffect} />
                </div>
                <CursorFollowingToggle
                  zoomEffect={demoZoomEffect}
                  onConfigChange={handleCursorFollowingChange}
                  simple={true}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator />

      {/* Current Configuration Display */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Current Configuration</h2>
        <Card>
          <CardContent className="pt-6">
            <pre className="text-xs bg-gray-50 p-4 rounded-md overflow-auto">
              {JSON.stringify(demoZoomEffect, null, 2)}
            </pre>
          </CardContent>
        </Card>
      </div>

      {/* Usage Instructions */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Integration Guide</h2>
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-3 text-sm">
              <div>
                <strong>🎛️ Global Configuration:</strong> Use <code>ZoomConfigPanel</code> for app-wide cursor following defaults
              </div>
              <div>
                <strong>⚙️ Effect Configuration:</strong> Use <code>CursorFollowingControls</code> for detailed per-effect settings
              </div>
              <div>
                <strong>🔄 Quick Toggle:</strong> Use <code>CursorFollowingToggle</code> for inline enable/disable controls
              </div>
              <div>
                <strong>📊 Status Display:</strong> Use <code>CursorFollowingStatus</code> for read-only status indicators
              </div>
              <div>
                <strong>⚡ Quick Actions:</strong> Use <code>CursorFollowingQuickActions</code> for preset configurations
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
