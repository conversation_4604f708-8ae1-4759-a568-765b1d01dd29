# Cursor Following Zoom - Testing Guide

## Overview
This guide provides comprehensive testing approaches for the cursor following zoom feature implementation.

## 🧪 Testing Methods

### 1. Unit Tests
Run the automated unit tests to verify core functionality:

```bash
npm test src/features/editor/utils/__tests__/cursor-following-zoom.test.ts
```

**What it tests:**
- Cursor position interpolation between frames
- Zoom area calculation with edge constraints
- Smooth transitions between zoom areas
- Integration with cursor data structures

### 2. Integration Tests with Mock Data
Use the test utility to simulate different cursor movement patterns:

```typescript
import { runAllCursorFollowingTests } from './src/features/editor/utils/test-cursor-following';

// Run all tests
runAllCursorFollowingTests();

// Or test specific patterns
testCursorFollowingZoom('circular', 1920, 1080, 5000);
testCursorFollowingZoom('zigzag', 1920, 1080, 5000);
testCursorFollowingZoom('linear', 1920, 1080, 5000);
```

### 3. Manual Testing with Real Data

#### Step 1: Create a Test Zoom Effect
Add a zoom effect with cursor following enabled to your video:

```typescript
const testZoomEffect = {
  id: 'cursor-following-test',
  startTime: 1000,  // Start at 1 second
  endTime: 6000,    // End at 6 seconds
  cursorFollowing: {
    enabled: true,
    zoomRadius: 0.15,        // 15% of canvas size
    smoothingFactor: 0.7,    // 70% smoothing
    edgePadding: 0.1,        // 10% edge padding
    minZoomScale: 1.5,       // Minimum 1.5x zoom
    maxZoomScale: 3.0,       // Maximum 3x zoom
  }
};
```

#### Step 2: Test with Chrome Extension Data
1. Record a screen session with the Chrome extension
2. Ensure cursor tracking is enabled
3. Import the recorded video with cursor data
4. Apply the cursor following zoom effect
5. Play the video and observe the zoom behavior

#### Step 3: Verify Expected Behavior
- ✅ Zoom area follows cursor movement smoothly
- ✅ Zoom stays within video boundaries
- ✅ Smooth transitions between cursor positions
- ✅ Respects zoom timing (start/end times)
- ✅ Zoom scale stays within configured limits

## 🔍 Test Scenarios

### Scenario 1: Basic Cursor Following
**Setup:**
- Canvas: 1920x1080
- Cursor moves from center to corners
- Zoom radius: 15%
- Duration: 5 seconds

**Expected Results:**
- Zoom smoothly follows cursor
- No jittery movements
- Zoom area stays within bounds

### Scenario 2: Fast Cursor Movement
**Setup:**
- Rapid cursor movements across screen
- High smoothing factor (0.8-0.9)
- Short zoom duration (2 seconds)

**Expected Results:**
- Smooth zoom transitions despite fast cursor
- No lag or stuttering
- Zoom catches up to cursor position

### Scenario 3: Edge Cases
**Setup:**
- Cursor near screen edges
- Very small zoom radius (5%)
- Large zoom radius (30%)

**Expected Results:**
- Zoom area constrained by edge padding
- No zoom area outside canvas bounds
- Graceful handling of extreme values

### Scenario 4: Multiple Zoom Effects
**Setup:**
- Multiple zoom effects with different cursor following settings
- Overlapping time ranges
- Different zoom parameters

**Expected Results:**
- Most recent effect takes precedence
- Smooth transitions between effects
- No conflicts between settings

## 🐛 Common Issues to Test

### 1. Performance Issues
- **Test:** Long videos (>10 minutes) with dense cursor data
- **Check:** No memory leaks or performance degradation
- **Monitor:** Frame rate during zoom calculations

### 2. Data Synchronization
- **Test:** Cursor data with different frame rates
- **Check:** Proper time-based interpolation
- **Verify:** Cursor position matches video timing

### 3. Edge Constraint Handling
- **Test:** Cursor at extreme positions (0,0) and (width,height)
- **Check:** Zoom area stays within bounds
- **Verify:** Edge padding is respected

### 4. Smoothing Behavior
- **Test:** Different smoothing factors (0.1, 0.5, 0.9)
- **Check:** Appropriate smoothness vs responsiveness
- **Verify:** No oscillation or instability

## 📊 Performance Benchmarks

### Target Performance Metrics:
- **Zoom Calculation Time:** < 1ms per frame
- **Memory Usage:** < 50MB for 10-minute video
- **Frame Rate Impact:** < 5% reduction during zoom
- **Cursor Position Lookup:** < 0.1ms per lookup

### Benchmark Test:
```typescript
// Performance test
const startTime = performance.now();
for (let i = 0; i < 1000; i++) {
  calculateCursorFollowingZoom(testParams);
}
const endTime = performance.now();
console.log(`Average calculation time: ${(endTime - startTime) / 1000}ms`);
```

## 🔧 Debugging Tools

### 1. Console Logging
Enable detailed logging in zoom calculations:
```typescript
// Add to zoom-calculations.ts
const DEBUG_CURSOR_FOLLOWING = true;

if (DEBUG_CURSOR_FOLLOWING) {
  console.log('Cursor Following Debug:', {
    currentTime,
    cursorPosition,
    zoomArea,
    zoomScale
  });
}
```

### 2. Visual Debugging
Add visual indicators to show:
- Current cursor position
- Zoom area boundaries
- Interpolated cursor path
- Edge constraints

### 3. Test Data Export
Export test results for analysis:
```typescript
const testResults = testCursorFollowingZoom('circular');
console.log(JSON.stringify(testResults, null, 2));
```

## ✅ Test Checklist

### Core Functionality
- [ ] Cursor position interpolation works correctly
- [ ] Zoom area calculation respects cursor position
- [ ] Edge constraints prevent zoom outside bounds
- [ ] Smoothing provides fluid transitions
- [ ] Zoom scale stays within configured limits

### Integration
- [ ] Works with VideoEditorComposition
- [ ] Integrates with existing zoom effects
- [ ] Handles cursor data from Chrome extension
- [ ] Transform origin updates correctly

### Performance
- [ ] No significant performance impact
- [ ] Handles large cursor datasets
- [ ] Memory usage remains stable
- [ ] Frame rate stays consistent

### Edge Cases
- [ ] Empty cursor data handled gracefully
- [ ] Missing cursor data at specific times
- [ ] Extreme cursor positions (edges/corners)
- [ ] Very fast cursor movements
- [ ] Very slow cursor movements

### User Experience
- [ ] Zoom follows cursor intuitively
- [ ] Smooth and natural movement
- [ ] No jarring transitions
- [ ] Respects user configuration

## 🚀 Next Steps

After testing, consider these improvements:
1. **Adaptive Smoothing:** Adjust smoothing based on cursor speed
2. **Predictive Zoom:** Anticipate cursor movement direction
3. **Performance Optimization:** Cache calculations for repeated frames
4. **Visual Feedback:** Show zoom area preview in timeline
5. **Advanced Patterns:** Support for custom cursor following patterns

## 📝 Test Results Template

```
Test Date: ___________
Tester: ___________
Browser: ___________
Video Resolution: ___________

✅ PASSED / ❌ FAILED

Basic Functionality:
- Cursor interpolation: ___
- Zoom area calculation: ___
- Edge constraints: ___
- Smoothing: ___

Performance:
- Calculation speed: ___
- Memory usage: ___
- Frame rate: ___

Edge Cases:
- Empty data: ___
- Edge positions: ___
- Fast movement: ___

Overall Rating: ___/10
Notes: ___________
```
