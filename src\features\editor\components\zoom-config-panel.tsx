import React from 'react';
import { useZoomStore } from '../store/use-zoom-store';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

/**
 * Demo component to show how centralized zoom configuration works
 * This component allows real-time modification of zoom parameters
 * that will affect both VideoEditorComposition and canvas-container
 */
export const ZoomConfigPanel: React.FC = () => {
  const {
    config,
    setMaxZoomScale,
    setBezierControlPoints,
    setDefaultTiming,
    setZoomOut,
    setCursorFollowing,
    resetToDefaults
  } = useZoomStore();

  const handleMaxZoomScaleChange = (value: string) => {
    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue > 0) {
      setMaxZoomScale(numValue);
    }
  };

  const handleBezierPointChange = (point: keyof typeof config.bezierControlPoints, value: string) => {
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setBezierControlPoints({ [point]: numValue });
    }
  };

  const handleTimingChange = (timing: keyof typeof config.defaultTiming, value: string) => {
    const numValue = parseInt(value);
    if (!isNaN(numValue) && numValue >= 0) {
      setDefaultTiming({ [timing]: numValue });
    }
  };

  const handleZoomOutChange = (property: keyof typeof config.zoomOut, value: string | boolean) => {
    if (property === 'enabled') {
      setZoomOut({ [property]: value as boolean });
    } else if (property === 'duration') {
      const numValue = parseInt(value as string);
      if (!isNaN(numValue) && numValue >= 0) {
        setZoomOut({ [property]: numValue });
      }
    } else if (property === 'easing') {
      setZoomOut({ [property]: value as 'linear' | 'ease-out' | 'bezier' });
    }
  };

  const handleCursorFollowingChange = (field: keyof typeof config.cursorFollowing, value: any) => {
    if (field === 'enabledByDefault') {
      setCursorFollowing({ enabledByDefault: value });
    } else if (field === 'defaultZoomRadius') {
      const numValue = parseFloat(value);
      if (!isNaN(numValue) && numValue > 0 && numValue <= 0.5) {
        setCursorFollowing({ defaultZoomRadius: numValue });
      }
    } else if (field === 'defaultSmoothingFactor') {
      const numValue = parseFloat(value);
      if (!isNaN(numValue) && numValue >= 0 && numValue <= 1) {
        setCursorFollowing({ defaultSmoothingFactor: numValue });
      }
    } else if (field === 'defaultEdgePadding') {
      const numValue = parseFloat(value);
      if (!isNaN(numValue) && numValue >= 0 && numValue <= 0.5) {
        setCursorFollowing({ defaultEdgePadding: numValue });
      }
    } else if (field === 'defaultMinZoomScale') {
      const numValue = parseFloat(value);
      if (!isNaN(numValue) && numValue >= 1 && numValue <= config.cursorFollowing.defaultMaxZoomScale) {
        setCursorFollowing({ defaultMinZoomScale: numValue });
      }
    } else if (field === 'defaultMaxZoomScale') {
      const numValue = parseFloat(value);
      if (!isNaN(numValue) && numValue >= config.cursorFollowing.defaultMinZoomScale && numValue <= 10) {
        setCursorFollowing({ defaultMaxZoomScale: numValue });
      }
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Zoom Configuration</CardTitle>
        <CardDescription>
          Centralized zoom settings that affect both export and player zoom behavior
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Max Zoom Scale */}
        <div className="space-y-2">
          <Label htmlFor="maxZoomScale">Max Zoom Scale</Label>
          <Input
            id="maxZoomScale"
            type="number"
            step="0.1"
            min="0.1"
            max="5"
            value={config.maxZoomScale}
            onChange={(e) => handleMaxZoomScaleChange(e.target.value)}
          />
          <p className="text-xs text-muted-foreground">
            Total zoom will be 1.0 + this value (current max: {1 + config.maxZoomScale}x)
          </p>
        </div>

        {/* Bezier Control Points */}
        <div className="space-y-2">
          <Label>Bezier Control Points</Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="p1" className="text-xs">P1</Label>
              <Input
                id="p1"
                type="number"
                step="0.01"
                value={config.bezierControlPoints.p1}
                onChange={(e) => handleBezierPointChange('p1', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="p2" className="text-xs">P2</Label>
              <Input
                id="p2"
                type="number"
                step="0.01"
                value={config.bezierControlPoints.p2}
                onChange={(e) => handleBezierPointChange('p2', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="p3" className="text-xs">P3</Label>
              <Input
                id="p3"
                type="number"
                step="0.01"
                value={config.bezierControlPoints.p3}
                onChange={(e) => handleBezierPointChange('p3', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="p4" className="text-xs">P4</Label>
              <Input
                id="p4"
                type="number"
                step="0.01"
                value={config.bezierControlPoints.p4}
                onChange={(e) => handleBezierPointChange('p4', e.target.value)}
              />
            </div>
          </div>
          <p className="text-xs text-muted-foreground">
            Controls the zoom animation curve shape
          </p>
        </div>

        {/* Default Timing */}
        <div className="space-y-2">
          <Label>Default Timing (milliseconds)</Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="startTime" className="text-xs">Start Time</Label>
              <Input
                id="startTime"
                type="number"
                step="100"
                min="0"
                value={config.defaultTiming.startTime}
                onChange={(e) => handleTimingChange('startTime', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="endTime" className="text-xs">End Time</Label>
              <Input
                id="endTime"
                type="number"
                step="100"
                min="0"
                value={config.defaultTiming.endTime}
                onChange={(e) => handleTimingChange('endTime', e.target.value)}
              />
            </div>
          </div>
          <p className="text-xs text-muted-foreground">
            Duration: {config.defaultTiming.endTime - config.defaultTiming.startTime}ms
          </p>
        </div>

        {/* Zoom-Out Configuration */}
        <div className="space-y-2">
          <Label>Zoom-Out Settings</Label>

          {/* Enable/Disable Zoom-Out */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="zoomOutEnabled"
              checked={config.zoomOut.enabled}
              onChange={(e) => handleZoomOutChange('enabled', e.target.checked)}
            />
            <Label htmlFor="zoomOutEnabled" className="text-sm">Enable smooth zoom-out</Label>
          </div>

          {/* Zoom-Out Duration */}
          {config.zoomOut.enabled && (
            <>
              <div>
                <Label htmlFor="zoomOutDuration" className="text-xs">Duration (ms)</Label>
                <Input
                  id="zoomOutDuration"
                  type="number"
                  step="100"
                  min="0"
                  value={config.zoomOut.duration}
                  onChange={(e) => handleZoomOutChange('duration', e.target.value)}
                />
              </div>

              {/* Zoom-Out Easing */}
              <div>
                <Label htmlFor="zoomOutEasing" className="text-xs">Easing</Label>
                <select
                  id="zoomOutEasing"
                  value={config.zoomOut.easing}
                  onChange={(e) => handleZoomOutChange('easing', e.target.value)}
                  className="w-full p-2 border rounded"
                >
                  <option value="linear">Linear</option>
                  <option value="ease-out">Ease Out</option>
                  <option value="bezier">Bezier (same as zoom-in)</option>
                </select>
              </div>
            </>
          )}

          <p className="text-xs text-muted-foreground">
            {config.zoomOut.enabled
              ? `Total zoom duration: ${(config.defaultTiming.endTime - config.defaultTiming.startTime) + config.zoomOut.duration}ms`
              : 'Zoom will instantly return to normal scale'
            }
          </p>
        </div>

        {/* Reset Button */}
        <Button
          variant="outline"
          onClick={resetToDefaults}
          className="w-full"
        >
          Reset to Defaults
        </Button>

        {/* Current Configuration Display */}
        <div className="mt-4 p-3 bg-muted rounded-md">
          <p className="text-xs font-medium mb-2">Current Configuration:</p>
          <pre className="text-xs overflow-auto">
            {JSON.stringify(config, null, 2)}
          </pre>
        </div>
      </CardContent>
    </Card>
  );
};
