import { IZoomConfig, IZoomTiming } from "../store/use-zoom-store";

/**
 * Cubic bezier helper function for smooth zoom animations
 * This creates smooth acceleration and deceleration curves
 *
 * @param t - Progress value between 0 and 1
 * @param p1 - First control point
 * @param p2 - Second control point
 * @param p3 - Third control point
 * @param p4 - Fourth control point
 * @returns Bezier curve value
 */
export function cubicBezier(t: number, p1: number, p2: number, p3: number, p4: number): number {
  const u = 1 - t;
  const tt = t * t;
  const uu = u * u;
  const uuu = uu * u;
  const ttt = tt * t;
  return 3 * uu * t * p2 + 3 * u * tt * p3 + ttt;
}

/**
 * Easing functions for zoom-out animation
 */
export function easeOut(t: number): number {
  return 1 - Math.pow(1 - t, 3);
}

export function linear(t: number): number {
  return t;
}

/**
 * Parameters for zoom scale calculation
 */
export interface IZoomCalculationParams {
  /** Current time or frame position */
  currentPosition: number;
  /** Whether currentPosition is in frames (true) or milliseconds (false) */
  isFrameBased: boolean;
  /** Frames per second (required if isFrameBased is true) */
  fps?: number;
  /** Zoom timing configuration */
  zoomTiming: IZoomTiming;
  /** Zoom configuration settings */
  zoomConfig: IZoomConfig;
  /** Zoom effect configuration (optional, for cursor following) */
  zoomEffect?: {
    cursorFollowing?: {
      enabled: boolean;
      zoomRadius: number;
      smoothingFactor: number;
      edgePadding: number;
      minZoomScale: number;
      maxZoomScale: number;
    };
  };
  /** Cursor data for cursor following mode (optional) */
  cursorData?: ICursorPosition[];
  /** Canvas dimensions for cursor following mode (optional) */
  canvasDimensions?: {
    width: number;
    height: number;
  };
  /** Previous zoom area for smoothing (optional) */
  previousZoomArea?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

/**
 * Result of zoom scale calculation
 */
export interface IZoomCalculationResult {
  /** The calculated zoom scale (1.0 = no zoom) */
  zoomScale: number;
  /** Whether zoom is currently active */
  isZoomActive: boolean;
  /** Progress through the zoom animation (0-1) */
  progress: number;
  /** Bezier-adjusted progress value */
  bezierProgress: number;
  /** Current zoom phase: 'zoom-in', 'zoom-out', or 'inactive' */
  phase: 'zoom-in' | 'zoom-out' | 'inactive';
  /** Dynamic zoom area (for cursor following mode) */
  dynamicZoomArea?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  /** Current cursor position (for cursor following mode) */
  currentCursorPosition?: ICursorPosition | null;
  /** Whether cursor following is active */
  isCursorFollowing?: boolean;
}

/**
 * Centralized zoom scale calculation function
 * This eliminates the duplicated zoom logic between VideoEditorComposition and canvas-container
 * 
 * @param params - Zoom calculation parameters
 * @returns Zoom calculation result
 */
export function calculateZoomScale(params: IZoomCalculationParams): IZoomCalculationResult {
  const {
    currentPosition,
    isFrameBased,
    fps,
    zoomTiming,
    zoomConfig,
    zoomEffect,
    cursorData,
    canvasDimensions,
    previousZoomArea
  } = params;

  // Convert position to time in milliseconds if needed
  let currentTime: number;
  if (isFrameBased) {
    if (!fps) {
      throw new Error("FPS is required when using frame-based calculations");
    }
    currentTime = (currentPosition / fps) * 1000;
  } else {
    currentTime = currentPosition;
  }

  // Check if cursor following mode is enabled
  const isCursorFollowing = zoomEffect?.cursorFollowing?.enabled === true;

  // Handle cursor following mode
  if (isCursorFollowing && cursorData && canvasDimensions) {
    const cursorFollowingResult = calculateCursorFollowingZoom({
      currentTime,
      cursorData,
      canvasWidth: canvasDimensions.width,
      canvasHeight: canvasDimensions.height,
      cursorConfig: {
        zoomRadius: zoomEffect.cursorFollowing!.zoomRadius,
        smoothingFactor: zoomEffect.cursorFollowing!.smoothingFactor,
        edgePadding: zoomEffect.cursorFollowing!.edgePadding,
        minZoomScale: zoomEffect.cursorFollowing!.minZoomScale,
        maxZoomScale: zoomEffect.cursorFollowing!.maxZoomScale,
      },
      previousZoomArea,
    });

    // For cursor following, we still need to respect the zoom timing
    // but use the cursor-calculated zoom scale and area
    const zoomStartTime = zoomTiming.startTime;
    const zoomEndTime = zoomTiming.endTime;
    const zoomDuration = zoomEndTime - zoomStartTime;

    // Check if we're within the zoom timing window
    const isWithinZoomWindow = currentTime >= zoomStartTime && currentTime <= zoomEndTime;

    if (!isWithinZoomWindow || zoomDuration <= 0) {
      return {
        zoomScale: 1,
        isZoomActive: false,
        progress: 0,
        bezierProgress: 0,
        phase: 'inactive',
        dynamicZoomArea: undefined,
        currentCursorPosition: cursorFollowingResult.currentCursorPosition,
        isCursorFollowing: false,
      };
    }

    // Calculate progress through the zoom timing
    const progress = Math.max(0, Math.min(1, (currentTime - zoomStartTime) / zoomDuration));

    // Apply bezier curve for smooth zoom animation
    const { p1, p2, p3, p4 } = zoomConfig.bezierControlPoints;
    const bezierProgress = cubicBezier(progress, p1, p2, p3, p4);

    // Use cursor-calculated zoom scale, but apply timing-based scaling
    const baseZoomScale = cursorFollowingResult.zoomScale;
    const timedZoomScale = 1 + (baseZoomScale - 1) * bezierProgress;

    return {
      zoomScale: timedZoomScale,
      isZoomActive: true,
      progress,
      bezierProgress,
      phase: 'zoom-in',
      dynamicZoomArea: cursorFollowingResult.zoomArea,
      currentCursorPosition: cursorFollowingResult.currentCursorPosition,
      isCursorFollowing: true,
    };
  }

  // Extract timing values
  const zoomStartTime = zoomTiming.startTime;
  const zoomEndTime = zoomTiming.endTime;
  const zoomDuration = zoomEndTime - zoomStartTime;

  // Early return for performance if outside zoom range
  if (zoomDuration <= 0) {
    return {
      zoomScale: 1,
      isZoomActive: false,
      progress: 0,
      bezierProgress: 0,
      phase: 'inactive',
      dynamicZoomArea: undefined,
      currentCursorPosition: undefined,
      isCursorFollowing: false,
    };
  }

  // Calculate zoom-out end time
  const zoomOutDuration = zoomConfig.zoomOut.enabled ? zoomConfig.zoomOut.duration : 0;
  const zoomOutEndTime = zoomEndTime + zoomOutDuration;

  // Early return if completely outside zoom range for better performance
  if (currentTime < zoomStartTime && (!zoomConfig.zoomOut.enabled || currentTime > zoomOutEndTime)) {
    return {
      zoomScale: 1,
      isZoomActive: false,
      progress: 0,
      bezierProgress: 0,
      phase: 'inactive',
      dynamicZoomArea: undefined,
      currentCursorPosition: undefined,
      isCursorFollowing: false,
    };
  }

  // Initialize result
  let zoomScale = 1;
  let isZoomActive = false;
  let progress = 0;
  let bezierProgress = 0;
  let phase: 'zoom-in' | 'zoom-out' | 'inactive' = 'inactive';

  // Check if we're in the zoom-in phase
  if (currentTime >= zoomStartTime && currentTime <= zoomEndTime) {
    isZoomActive = true;
    phase = 'zoom-in';

    // Calculate progress through the zoom-in animation (0 to 1)
    progress = (currentTime - zoomStartTime) / zoomDuration;

    // Use sine wave to create smooth zoom in and out within the duration
    // sin(0) = 0, sin(π/2) = 1, sin(π) = 0
    // This creates a smooth zoom that peaks in the middle and returns to 1.0 at the end
    bezierProgress = Math.sin(progress * Math.PI);

    // Calculate final zoom scale
    zoomScale = 1 + bezierProgress * zoomConfig.maxZoomScale;
  }
  // Optional zoom-out phase: only used if someone wants additional zoom-out after the main cycle
  // Note: The main zoom cycle now completes within the zoom duration using sine wave
  else if (zoomConfig.zoomOut.enabled && zoomOutDuration > 0 &&
           currentTime > zoomEndTime && currentTime <= zoomOutEndTime) {
    isZoomActive = true;
    phase = 'zoom-out';

    // Calculate progress through the zoom-out animation (0 to 1)
    progress = (currentTime - zoomEndTime) / zoomOutDuration;

    // Apply easing function based on configuration
    let easedProgress: number;
    switch (zoomConfig.zoomOut.easing) {
      case 'linear':
        easedProgress = linear(progress);
        break;
      case 'ease-out':
        easedProgress = easeOut(progress);
        break;
      case 'bezier':
        const { p1, p2, p3, p4 } = zoomConfig.bezierControlPoints;
        easedProgress = cubicBezier(progress, p1, p2, p3, p4);
        break;
      default:
        easedProgress = easeOut(progress);
    }

    bezierProgress = easedProgress;

    // Since the main zoom cycle already returned to 1.0, this phase starts from 1.0
    // and can zoom out further if desired (scale < 1.0) or do nothing
    zoomScale = 1.0; // Keep at normal scale since main cycle already completed
  }

  return {
    zoomScale,
    isZoomActive,
    progress,
    bezierProgress,
    phase,
    dynamicZoomArea: undefined,
    currentCursorPosition: undefined,
    isCursorFollowing: false,
  };
}

/**
 * Convenience function for frame-based zoom calculation (used by Remotion)
 *
 * @param currentFrame - Current frame number
 * @param fps - Frames per second
 * @param zoomTiming - Zoom timing configuration
 * @param zoomConfig - Zoom configuration settings
 * @param additionalParams - Additional parameters for cursor following mode
 * @returns Zoom calculation result
 */
export function calculateZoomScaleFromFrame(
  currentFrame: number,
  fps: number,
  zoomTiming: IZoomTiming,
  zoomConfig: IZoomConfig,
  additionalParams?: {
    zoomEffect?: any;
    cursorData?: ICursorPosition[];
    canvasDimensions?: { width: number; height: number };
    previousZoomArea?: { x: number; y: number; width: number; height: number };
  }
): IZoomCalculationResult {
  return calculateZoomScale({
    currentPosition: currentFrame,
    isFrameBased: true,
    fps,
    zoomTiming,
    zoomConfig,
    zoomEffect: additionalParams?.zoomEffect,
    cursorData: additionalParams?.cursorData,
    canvasDimensions: additionalParams?.canvasDimensions,
    previousZoomArea: additionalParams?.previousZoomArea,
  });
}

/**
 * Convenience function for time-based zoom calculation (used by Player)
 *
 * @param currentTime - Current time in milliseconds
 * @param zoomTiming - Zoom timing configuration
 * @param zoomConfig - Zoom configuration settings
 * @param additionalParams - Additional parameters for cursor following mode
 * @returns Zoom calculation result
 */
export function calculateZoomScaleFromTime(
  currentTime: number,
  zoomTiming: IZoomTiming,
  zoomConfig: IZoomConfig,
  additionalParams?: {
    zoomEffect?: any;
    cursorData?: ICursorPosition[];
    canvasDimensions?: { width: number; height: number };
    previousZoomArea?: { x: number; y: number; width: number; height: number };
  }
): IZoomCalculationResult {
  return calculateZoomScale({
    currentPosition: currentTime,
    isFrameBased: false,
    zoomTiming,
    zoomConfig,
    zoomEffect: additionalParams?.zoomEffect,
    cursorData: additionalParams?.cursorData,
    canvasDimensions: additionalParams?.canvasDimensions,
    previousZoomArea: additionalParams?.previousZoomArea,
  });
}

// ============================================================================
// CURSOR FOLLOWING ZOOM UTILITIES
// ============================================================================

/**
 * Cursor position data structure (matches CursorPoint from CursorOverlay)
 */
export interface ICursorPosition {
  x: number;
  y: number;
  frame: number;
  time: number;
  action: 'move' | 'click';
}

/**
 * Parameters for cursor following zoom calculation
 */
export interface ICursorFollowingZoomParams {
  /** Current time in milliseconds */
  currentTime: number;
  /** Cursor data array */
  cursorData: ICursorPosition[];
  /** Video canvas dimensions */
  canvasWidth: number;
  canvasHeight: number;
  /** Cursor following configuration */
  cursorConfig: {
    zoomRadius: number;
    smoothingFactor: number;
    edgePadding: number;
    minZoomScale: number;
    maxZoomScale: number;
  };
  /** Previous zoom area for smoothing (optional) */
  previousZoomArea?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

/**
 * Result of cursor following zoom calculation
 */
export interface ICursorFollowingZoomResult {
  /** Calculated zoom area based on cursor position */
  zoomArea: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  /** Calculated zoom scale */
  zoomScale: number;
  /** Current cursor position (interpolated if needed) */
  currentCursorPosition: ICursorPosition | null;
  /** Whether cursor data is available at current time */
  hasCursorData: boolean;
}

/**
 * Find the cursor position at a specific time with interpolation
 */
export function getCursorPositionAtTime(
  cursorData: ICursorPosition[],
  targetTime: number
): ICursorPosition | null {
  if (!cursorData || cursorData.length === 0) {
    return null;
  }

  // Sort cursor data by time to ensure proper interpolation
  const sortedData = [...cursorData].sort((a, b) => a.time - b.time);

  // Find exact match first
  const exactMatch = sortedData.find(cursor => Math.abs(cursor.time - targetTime) < 16); // ~1 frame tolerance at 60fps
  if (exactMatch) {
    return exactMatch;
  }

  // Find the two cursor positions to interpolate between
  let beforeCursor: ICursorPosition | null = null;
  let afterCursor: ICursorPosition | null = null;

  for (let i = 0; i < sortedData.length; i++) {
    const cursor = sortedData[i];
    if (cursor.time <= targetTime) {
      beforeCursor = cursor;
    } else {
      afterCursor = cursor;
      break;
    }
  }

  // If we only have data before the target time, use the last known position
  if (beforeCursor && !afterCursor) {
    return beforeCursor;
  }

  // If we only have data after the target time, use the first known position
  if (!beforeCursor && afterCursor) {
    return afterCursor;
  }

  // If we have both, interpolate between them
  if (beforeCursor && afterCursor) {
    const timeDiff = afterCursor.time - beforeCursor.time;
    const progress = timeDiff > 0 ? (targetTime - beforeCursor.time) / timeDiff : 0;

    return {
      x: beforeCursor.x + (afterCursor.x - beforeCursor.x) * progress,
      y: beforeCursor.y + (afterCursor.y - beforeCursor.y) * progress,
      frame: Math.round(beforeCursor.frame + (afterCursor.frame - beforeCursor.frame) * progress),
      time: targetTime,
      action: progress < 0.5 ? beforeCursor.action : afterCursor.action,
    };
  }

  return null;
}

/**
 * Calculate zoom area based on cursor position with edge constraints
 */
export function calculateCursorZoomArea(
  cursorX: number,
  cursorY: number,
  canvasWidth: number,
  canvasHeight: number,
  zoomRadius: number,
  edgePadding: number
): { x: number; y: number; width: number; height: number } {
  // Convert cursor position to normalized coordinates (0-1)
  const normalizedX = cursorX / canvasWidth;
  const normalizedY = cursorY / canvasHeight;

  // Calculate zoom area dimensions (diameter = 2 * radius)
  const zoomWidth = zoomRadius * 2;
  const zoomHeight = zoomRadius * 2;

  // Calculate initial zoom area position (centered on cursor)
  let zoomX = normalizedX - zoomRadius;
  let zoomY = normalizedY - zoomRadius;

  // Apply edge constraints with padding
  const minX = edgePadding;
  const minY = edgePadding;
  const maxX = 1 - edgePadding - zoomWidth;
  const maxY = 1 - edgePadding - zoomHeight;

  // Clamp zoom area to stay within bounds
  zoomX = Math.max(minX, Math.min(maxX, zoomX));
  zoomY = Math.max(minY, Math.min(maxY, zoomY));

  return {
    x: zoomX,
    y: zoomY,
    width: zoomWidth,
    height: zoomHeight,
  };
}

/**
 * Apply smoothing to zoom area transitions
 */
export function smoothZoomArea(
  currentArea: { x: number; y: number; width: number; height: number },
  previousArea: { x: number; y: number; width: number; height: number } | undefined,
  smoothingFactor: number
): { x: number; y: number; width: number; height: number } {
  if (!previousArea || smoothingFactor <= 0) {
    return currentArea;
  }

  const smoothing = Math.max(0, Math.min(1, smoothingFactor));

  return {
    x: previousArea.x + (currentArea.x - previousArea.x) * (1 - smoothing),
    y: previousArea.y + (currentArea.y - previousArea.y) * (1 - smoothing),
    width: previousArea.width + (currentArea.width - previousArea.width) * (1 - smoothing),
    height: previousArea.height + (currentArea.height - previousArea.height) * (1 - smoothing),
  };
}

/**
 * Main cursor following zoom calculation function
 */
export function calculateCursorFollowingZoom(
  params: ICursorFollowingZoomParams
): ICursorFollowingZoomResult {
  const {
    currentTime,
    cursorData,
    canvasWidth,
    canvasHeight,
    cursorConfig,
    previousZoomArea,
  } = params;

  // Get cursor position at current time
  const currentCursorPosition = getCursorPositionAtTime(cursorData, currentTime);

  if (!currentCursorPosition) {
    // No cursor data available, return default zoom area
    const defaultArea = {
      x: 0.25,
      y: 0.25,
      width: 0.5,
      height: 0.5,
    };

    return {
      zoomArea: defaultArea,
      zoomScale: cursorConfig.minZoomScale,
      currentCursorPosition: null,
      hasCursorData: false,
    };
  }

  // Calculate zoom area based on cursor position
  const rawZoomArea = calculateCursorZoomArea(
    currentCursorPosition.x,
    currentCursorPosition.y,
    canvasWidth,
    canvasHeight,
    cursorConfig.zoomRadius,
    cursorConfig.edgePadding
  );

  // Apply smoothing if previous area is available
  const smoothedZoomArea = smoothZoomArea(
    rawZoomArea,
    previousZoomArea,
    cursorConfig.smoothingFactor
  );

  // Calculate zoom scale based on area size
  const minDimension = Math.min(smoothedZoomArea.width, smoothedZoomArea.height);
  const zoomScale = Math.max(
    cursorConfig.minZoomScale,
    Math.min(
      cursorConfig.maxZoomScale,
      1 / Math.max(0.1, minDimension) // Prevent division by zero
    )
  );

  return {
    zoomArea: smoothedZoomArea,
    zoomScale,
    currentCursorPosition,
    hasCursorData: true,
  };
}
