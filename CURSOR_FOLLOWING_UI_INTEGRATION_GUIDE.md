# 🎯 Cursor Following UI Integration Guide

This guide explains how to integrate the cursor following UI components into your video editor interface.

## 📦 Available Components

### 1. **ZoomConfigPanel** (Enhanced)
**Location:** `src/features/editor/components/zoom-config-panel.tsx`
**Purpose:** Global cursor following configuration with app-wide defaults

```tsx
import { ZoomConfigPanel } from '../features/editor/components/zoom-config-panel';

// Use in settings/preferences panel
<ZoomConfigPanel />
```

**Features:**
- ✅ Enable/disable cursor following by default
- ⚙️ Configure default zoom radius, smoothing, edge padding
- 📏 Set default zoom scale range (min/max)
- 🔄 Reset to defaults button
- 📊 Real-time configuration preview

### 2. **CursorFollowingControls**
**Location:** `src/features/editor/components/cursor-following-controls.tsx`
**Purpose:** Detailed cursor following configuration for individual zoom effects

```tsx
import { CursorFollowingControls } from '../features/editor/components/cursor-following-controls';

// Full configuration panel
<CursorFollowingControls
  zoomEffect={selectedZoomEffect}
  onConfigChange={handleCursorFollowingChange}
/>

// Compact version for inline use
<CursorFollowingControls
  zoomEffect={selectedZoomEffect}
  onConfigChange={handleCursorFollowingChange}
  compact={true}
/>
```

**Props:**
- `zoomEffect?: IZoomEffect` - The zoom effect to configure
- `onConfigChange?: (config) => void` - Configuration change callback
- `compact?: boolean` - Use compact layout (default: false)
- `disabled?: boolean` - Disable all controls (default: false)

### 3. **CursorFollowingToggle**
**Location:** `src/features/editor/components/cursor-following-toggle.tsx`
**Purpose:** Quick toggle with optional configuration popover

```tsx
import { CursorFollowingToggle } from '../features/editor/components/cursor-following-toggle';

// Full toggle with configuration popover
<CursorFollowingToggle
  zoomEffect={selectedZoomEffect}
  onConfigChange={handleCursorFollowingChange}
/>

// Simple switch without popover
<CursorFollowingToggle
  zoomEffect={selectedZoomEffect}
  onConfigChange={handleCursorFollowingChange}
  simple={true}
/>
```

### 4. **CursorFollowingStatus**
**Location:** `src/features/editor/components/cursor-following-toggle.tsx`
**Purpose:** Read-only status indicator for timeline and preview areas

```tsx
import { CursorFollowingStatus } from '../features/editor/components/cursor-following-toggle';

// Simple status badge
<CursorFollowingStatus zoomEffect={zoomEffect} />

// Detailed status with configuration info
<CursorFollowingStatus zoomEffect={zoomEffect} detailed={true} />
```

### 5. **CursorFollowingQuickActions**
**Location:** `src/features/editor/components/cursor-following-toggle.tsx`
**Purpose:** One-click preset configurations

```tsx
import { CursorFollowingQuickActions } from '../features/editor/components/cursor-following-toggle';

<CursorFollowingQuickActions
  zoomEffect={selectedZoomEffect}
  onConfigChange={handleCursorFollowingChange}
/>
```

**Presets:**
- 🌊 **Gentle**: Large radius (20%), high smoothing (90%), gentle zoom (1.3x-2.5x)
- ⚡ **Responsive**: Medium radius (15%), balanced smoothing (70%), standard zoom (1.5x-3.0x)
- 🎯 **Precise**: Small radius (10%), low smoothing (50%), strong zoom (2.0x-4.0x)

## 🔧 Integration Examples

### Timeline Zoom Effect Overlay

```tsx
// In your zoom effect timeline component
import { CursorFollowingToggle, CursorFollowingStatus } from '../components/cursor-following-toggle';

const ZoomEffectTimelineBar = ({ zoomEffect, onUpdate }) => {
  return (
    <div className="zoom-effect-bar">
      {/* Effect duration bar */}
      <div className="effect-duration">
        {/* Cursor following indicator stripe */}
        {zoomEffect.cursorFollowing?.enabled && (
          <div className="cursor-following-indicator" />
        )}
      </div>
      
      {/* Controls */}
      <div className="effect-controls">
        <CursorFollowingStatus zoomEffect={zoomEffect} />
        <CursorFollowingToggle
          zoomEffect={zoomEffect}
          onConfigChange={(config) => onUpdate({ ...zoomEffect, cursorFollowing: config })}
          simple={true}
        />
      </div>
    </div>
  );
};
```

### Zoom Effect Properties Panel

```tsx
// In your zoom effect properties panel
import { CursorFollowingControls } from '../components/cursor-following-controls';

const ZoomEffectPropertiesPanel = ({ selectedZoomEffect, onUpdateZoomEffect }) => {
  return (
    <div className="properties-panel">
      {/* Standard zoom controls */}
      <ZoomTimingControls />
      <ZoomScaleControls />
      <ZoomPositionControls />
      
      {/* Cursor following controls */}
      <CursorFollowingControls
        zoomEffect={selectedZoomEffect}
        onConfigChange={(config) => 
          onUpdateZoomEffect({ 
            ...selectedZoomEffect, 
            cursorFollowing: config 
          })
        }
      />
    </div>
  );
};
```

### Quick Actions Toolbar

```tsx
// In your toolbar or quick actions area
import { CursorFollowingQuickActions } from '../components/cursor-following-toggle';

const ZoomEffectToolbar = ({ selectedZoomEffect, onUpdateZoomEffect }) => {
  return (
    <div className="toolbar">
      <CursorFollowingQuickActions
        zoomEffect={selectedZoomEffect}
        onConfigChange={(config) => 
          onUpdateZoomEffect({ 
            ...selectedZoomEffect, 
            cursorFollowing: config 
          })
        }
      />
    </div>
  );
};
```

## 🎨 Styling and Theming

All components use your existing UI component library and follow these design patterns:

### Color Coding
- 🟢 **Green**: Active cursor following
- 🔵 **Blue**: Cursor following configuration areas
- ⚫ **Gray**: Disabled or inactive states
- 🟡 **Yellow**: Custom configurations

### Visual Indicators
- ✅ **Checkmarks**: Enabled features
- 🎯 **Target Icon**: Cursor following mode
- ⚙️ **Gear Icon**: Configuration options
- 📊 **Badges**: Status indicators

## 🔄 State Management Integration

### Updating Zoom Effects

```tsx
// Example state update handler
const handleCursorFollowingChange = (config: IZoomEffect['cursorFollowing']) => {
  // Update the zoom effect in your store
  updateZoomEffect(selectedZoomEffectId, {
    ...selectedZoomEffect,
    cursorFollowing: config
  });
  
  // Optionally trigger re-render or preview update
  refreshVideoPreview();
};
```

### Global Configuration

```tsx
// Access global cursor following defaults
import { useZoomStore } from '../store/use-zoom-store';

const { config, setCursorFollowing } = useZoomStore();

// Use global defaults for new zoom effects
const createNewZoomEffect = () => {
  return {
    id: generateId(),
    startTime: currentTime,
    endTime: currentTime + 3000,
    cursorFollowing: {
      enabled: config.cursorFollowing.enabledByDefault,
      zoomRadius: config.cursorFollowing.defaultZoomRadius,
      smoothingFactor: config.cursorFollowing.defaultSmoothingFactor,
      edgePadding: config.cursorFollowing.defaultEdgePadding,
      minZoomScale: config.cursorFollowing.defaultMinZoomScale,
      maxZoomScale: config.cursorFollowing.defaultMaxZoomScale,
    }
  };
};
```

## 🧪 Testing the Integration

### Demo Component
Use the comprehensive demo to test all components:

```tsx
import { CursorFollowingUIDemo } from '../components/CursorFollowingUIDemo';

// Add to your development/testing routes
<CursorFollowingUIDemo />
```

### Manual Testing Checklist
- [ ] Global configuration updates affect new zoom effects
- [ ] Individual effect configuration works independently
- [ ] Toggle switches enable/disable cursor following
- [ ] Sliders and inputs update values correctly
- [ ] Preset buttons apply correct configurations
- [ ] Status indicators show correct states
- [ ] Popover controls work in compact mode
- [ ] All components handle disabled state properly

## 🚀 Next Steps

1. **Integrate into Timeline**: Add cursor following indicators to zoom effect timeline bars
2. **Add Keyboard Shortcuts**: Implement hotkeys for quick cursor following toggle
3. **Visual Feedback**: Add preview animations showing cursor following behavior
4. **Batch Operations**: Allow applying cursor following to multiple zoom effects
5. **Templates**: Create cursor following templates for common use cases

## 📝 Notes

- All components are fully typed with TypeScript
- Components follow your existing design system
- State management integrates with existing Zustand stores
- All components handle edge cases and validation
- Responsive design works on mobile and desktop
