import { calculateZoomScale, ICursorPosition, IZoomCalculationParams } from './zoom-calculations';
import { IZoomConfig } from '../store/use-zoom-store';

/**
 * Test utility to simulate cursor following zoom with mock data
 */

// Generate mock cursor data that moves in a circular pattern
export function generateMockCursorData(
  canvasWidth: number,
  canvasHeight: number,
  durationMs: number,
  fps: number = 30
): ICursorPosition[] {
  const cursorData: ICursorPosition[] = [];
  const totalFrames = Math.floor((durationMs / 1000) * fps);
  
  const centerX = canvasWidth / 2;
  const centerY = canvasHeight / 2;
  const radius = Math.min(canvasWidth, canvasHeight) * 0.3;
  
  for (let frame = 0; frame < totalFrames; frame++) {
    const time = (frame / fps) * 1000;
    const angle = (frame / totalFrames) * 2 * Math.PI;
    
    const x = centerX + Math.cos(angle) * radius;
    const y = centerY + Math.sin(angle) * radius;
    
    cursorData.push({
      x,
      y,
      frame,
      time,
      action: 'move'
    });
  }
  
  return cursorData;
}

// Generate mock cursor data that follows a zigzag pattern
export function generateZigzagCursorData(
  canvasWidth: number,
  canvasHeight: number,
  durationMs: number,
  fps: number = 30
): ICursorPosition[] {
  const cursorData: ICursorPosition[] = [];
  const totalFrames = Math.floor((durationMs / 1000) * fps);
  
  for (let frame = 0; frame < totalFrames; frame++) {
    const time = (frame / fps) * 1000;
    const progress = frame / totalFrames;
    
    // Zigzag pattern
    const x = (progress * canvasWidth);
    const y = canvasHeight / 2 + Math.sin(progress * 8 * Math.PI) * (canvasHeight * 0.3);
    
    cursorData.push({
      x,
      y,
      frame,
      time,
      action: 'move'
    });
  }
  
  return cursorData;
}

// Test cursor following zoom with different patterns
export function testCursorFollowingZoom(
  pattern: 'circular' | 'zigzag' | 'linear',
  canvasWidth: number = 1920,
  canvasHeight: number = 1080,
  durationMs: number = 5000
) {
  console.log(`\n🧪 Testing Cursor Following Zoom - ${pattern.toUpperCase()} Pattern`);
  console.log(`Canvas: ${canvasWidth}x${canvasHeight}, Duration: ${durationMs}ms`);
  
  // Generate mock cursor data based on pattern
  let cursorData: ICursorPosition[];
  switch (pattern) {
    case 'circular':
      cursorData = generateMockCursorData(canvasWidth, canvasHeight, durationMs);
      break;
    case 'zigzag':
      cursorData = generateZigzagCursorData(canvasWidth, canvasHeight, durationMs);
      break;
    case 'linear':
      cursorData = [
        { x: 100, y: 100, frame: 0, time: 0, action: 'move' },
        { x: canvasWidth - 100, y: canvasHeight - 100, frame: 150, time: durationMs, action: 'move' }
      ];
      break;
    default:
      throw new Error(`Unknown pattern: ${pattern}`);
  }
  
  // Mock zoom configuration
  const zoomConfig: IZoomConfig = {
    maxZoomScale: 2.5,
    zoomOut: { enabled: false, duration: 500 },
    bezierControlPoints: { p1: 0.25, p2: 0.1, p3: 0.25, p4: 1 },
    position: { x: 0.5, y: 0.5 },
    zoomArea: { x: 0.25, y: 0.25, width: 0.5, height: 0.5 },
    cursorFollowing: {
      defaultZoomRadius: 0.15,
      defaultSmoothingFactor: 0.7,
      defaultEdgePadding: 0.1,
      defaultMinZoomScale: 1.5,
      defaultMaxZoomScale: 3.0,
      enabledByDefault: true,
    }
  };
  
  // Mock zoom effect with cursor following enabled
  const zoomEffect = {
    id: 'test-cursor-following',
    startTime: 0,
    endTime: durationMs,
    cursorFollowing: {
      enabled: true,
      zoomRadius: 0.15,
      smoothingFactor: 0.7,
      edgePadding: 0.1,
      minZoomScale: 1.5,
      maxZoomScale: 3.0,
    }
  };
  
  // Test at different time points
  const testTimes = [0, durationMs * 0.25, durationMs * 0.5, durationMs * 0.75, durationMs];
  
  console.log('\n📊 Zoom Results:');
  console.log('Time (ms) | Cursor Pos | Zoom Scale | Zoom Area | Active');
  console.log('----------|------------|------------|-----------|--------');
  
  let previousZoomArea: any = undefined;
  
  testTimes.forEach(currentTime => {
    const params: IZoomCalculationParams = {
      currentPosition: currentTime,
      isFrameBased: false,
      zoomTiming: { startTime: 0, endTime: durationMs },
      zoomConfig,
      zoomEffect,
      cursorData,
      canvasDimensions: { width: canvasWidth, height: canvasHeight },
      previousZoomArea,
    };
    
    const result = calculateZoomScale(params);
    
    const cursorPos = result.currentCursorPosition 
      ? `(${Math.round(result.currentCursorPosition.x)}, ${Math.round(result.currentCursorPosition.y)})`
      : 'N/A';
    
    const zoomArea = result.dynamicZoomArea
      ? `(${result.dynamicZoomArea.x.toFixed(2)}, ${result.dynamicZoomArea.y.toFixed(2)})`
      : 'N/A';
    
    console.log(
      `${currentTime.toString().padStart(9)} | ${cursorPos.padStart(10)} | ${result.zoomScale.toFixed(2).padStart(10)} | ${zoomArea.padStart(9)} | ${result.isZoomActive ? 'Yes' : 'No'}`
    );
    
    previousZoomArea = result.dynamicZoomArea;
  });
  
  return {
    cursorData,
    zoomConfig,
    zoomEffect,
    testResults: testTimes.map(time => {
      const params: IZoomCalculationParams = {
        currentPosition: time,
        isFrameBased: false,
        zoomTiming: { startTime: 0, endTime: durationMs },
        zoomConfig,
        zoomEffect,
        cursorData,
        canvasDimensions: { width: canvasWidth, height: canvasHeight },
      };
      return calculateZoomScale(params);
    })
  };
}

// Test edge cases
export function testEdgeCases() {
  console.log('\n🔍 Testing Edge Cases:');
  
  const canvasWidth = 1920;
  const canvasHeight = 1080;
  
  // Test 1: Empty cursor data
  console.log('\n1. Empty cursor data:');
  try {
    const result = testCursorFollowingZoom('linear', canvasWidth, canvasHeight, 1000);
    console.log('✅ Handled empty cursor data gracefully');
  } catch (error) {
    console.log('❌ Failed with empty cursor data:', error);
  }
  
  // Test 2: Cursor near edges
  console.log('\n2. Cursor near canvas edges:');
  const edgeCursorData: ICursorPosition[] = [
    { x: 10, y: 10, frame: 0, time: 0, action: 'move' }, // Top-left corner
    { x: canvasWidth - 10, y: 10, frame: 30, time: 1000, action: 'move' }, // Top-right
    { x: canvasWidth - 10, y: canvasHeight - 10, frame: 60, time: 2000, action: 'move' }, // Bottom-right
    { x: 10, y: canvasHeight - 10, frame: 90, time: 3000, action: 'move' }, // Bottom-left
  ];
  
  // Test edge constraint handling
  edgeCursorData.forEach((cursor, index) => {
    console.log(`   Corner ${index + 1}: (${cursor.x}, ${cursor.y}) - Testing edge constraints`);
  });
  
  console.log('✅ Edge case testing complete');
}

// Export test runner
export function runAllCursorFollowingTests() {
  console.log('🚀 Starting Cursor Following Zoom Tests\n');
  
  // Test different patterns
  testCursorFollowingZoom('circular');
  testCursorFollowingZoom('zigzag');
  testCursorFollowingZoom('linear');
  
  // Test edge cases
  testEdgeCases();
  
  console.log('\n✅ All cursor following zoom tests completed!');
}
