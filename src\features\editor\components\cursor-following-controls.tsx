import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { useZoomStore } from '../store/use-zoom-store';
import { IZoomEffect } from '../store/use-store';

interface CursorFollowingControlsProps {
  /** The zoom effect to configure */
  zoomEffect?: IZoomEffect;
  /** Callback when cursor following configuration changes */
  onConfigChange?: (config: IZoomEffect['cursorFollowing']) => void;
  /** Whether to show as a compact inline control */
  compact?: boolean;
  /** Whether the controls are disabled */
  disabled?: boolean;
}

/**
 * Cursor Following Controls Component
 * 
 * Provides UI controls for configuring cursor following behavior on zoom effects.
 * Can be used both as a standalone configuration panel and as inline controls
 * within zoom effect editors.
 */
export const CursorFollowingControls: React.FC<CursorFollowingControlsProps> = ({
  zoomEffect,
  onConfigChange,
  compact = false,
  disabled = false
}) => {
  const { config: globalConfig } = useZoomStore();

  // Use effect-specific config or fall back to global defaults
  const currentConfig = zoomEffect?.cursorFollowing || {
    enabled: globalConfig.cursorFollowing.enabledByDefault,
    zoomRadius: globalConfig.cursorFollowing.defaultZoomRadius,
    smoothingFactor: globalConfig.cursorFollowing.defaultSmoothingFactor,
    edgePadding: globalConfig.cursorFollowing.defaultEdgePadding,
    minZoomScale: globalConfig.cursorFollowing.defaultMinZoomScale,
    maxZoomScale: globalConfig.cursorFollowing.defaultMaxZoomScale,
  };

  const handleConfigChange = (field: keyof typeof currentConfig, value: any) => {
    const newConfig = { ...currentConfig, [field]: value };
    onConfigChange?.(newConfig);
  };

  const handleSliderChange = (field: keyof typeof currentConfig, values: number[]) => {
    handleConfigChange(field, values[0]);
  };

  const resetToDefaults = () => {
    const defaultConfig = {
      enabled: globalConfig.cursorFollowing.enabledByDefault,
      zoomRadius: globalConfig.cursorFollowing.defaultZoomRadius,
      smoothingFactor: globalConfig.cursorFollowing.defaultSmoothingFactor,
      edgePadding: globalConfig.cursorFollowing.defaultEdgePadding,
      minZoomScale: globalConfig.cursorFollowing.defaultMinZoomScale,
      maxZoomScale: globalConfig.cursorFollowing.defaultMaxZoomScale,
    };
    onConfigChange?.(defaultConfig);
  };

  if (compact) {
    return (
      <div className="space-y-3">
        {/* Compact Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">🎯 Cursor Following</span>
            <Switch
              checked={currentConfig.enabled}
              onCheckedChange={(checked) => handleConfigChange('enabled', checked)}
              disabled={disabled}
            />
          </div>
          {currentConfig.enabled && (
            <Button
              variant="ghost"
              size="sm"
              onClick={resetToDefaults}
              disabled={disabled}
              className="text-xs"
            >
              Reset
            </Button>
          )}
        </div>

        {/* Compact Controls */}
        {currentConfig.enabled && (
          <div className="grid grid-cols-2 gap-3 p-3 bg-blue-50 rounded-md">
            <div>
              <Label className="text-xs">Radius</Label>
              <Slider
                value={[currentConfig.zoomRadius]}
                onValueChange={(values) => handleSliderChange('zoomRadius', values)}
                min={0.05}
                max={0.5}
                step={0.01}
                disabled={disabled}
                className="mt-1"
              />
              <span className="text-xs text-muted-foreground">{(currentConfig.zoomRadius * 100).toFixed(0)}%</span>
            </div>
            <div>
              <Label className="text-xs">Smoothing</Label>
              <Slider
                value={[currentConfig.smoothingFactor]}
                onValueChange={(values) => handleSliderChange('smoothingFactor', values)}
                min={0}
                max={1}
                step={0.1}
                disabled={disabled}
                className="mt-1"
              />
              <span className="text-xs text-muted-foreground">{(currentConfig.smoothingFactor * 100).toFixed(0)}%</span>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <span>🎯 Cursor Following</span>
          <Switch
            checked={currentConfig.enabled}
            onCheckedChange={(checked) => handleConfigChange('enabled', checked)}
            disabled={disabled}
          />
        </CardTitle>
        <CardDescription>
          Configure automatic zoom following cursor movement from screen recordings
        </CardDescription>
      </CardHeader>
      
      {currentConfig.enabled && (
        <CardContent className="space-y-4">
          {/* Zoom Radius */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="zoomRadius">Zoom Radius</Label>
              <span className="text-sm text-muted-foreground">{(currentConfig.zoomRadius * 100).toFixed(0)}%</span>
            </div>
            <Slider
              id="zoomRadius"
              value={[currentConfig.zoomRadius]}
              onValueChange={(values) => handleSliderChange('zoomRadius', values)}
              min={0.05}
              max={0.5}
              step={0.01}
              disabled={disabled}
            />
            <p className="text-xs text-muted-foreground">
              Size of the area around the cursor to zoom into
            </p>
          </div>

          {/* Smoothing Factor */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="smoothingFactor">Smoothing</Label>
              <span className="text-sm text-muted-foreground">{(currentConfig.smoothingFactor * 100).toFixed(0)}%</span>
            </div>
            <Slider
              id="smoothingFactor"
              value={[currentConfig.smoothingFactor]}
              onValueChange={(values) => handleSliderChange('smoothingFactor', values)}
              min={0}
              max={1}
              step={0.1}
              disabled={disabled}
            />
            <p className="text-xs text-muted-foreground">
              How smoothly the zoom follows cursor movement (0% = instant, 100% = very smooth)
            </p>
          </div>

          {/* Edge Padding */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="edgePadding">Edge Padding</Label>
              <span className="text-sm text-muted-foreground">{(currentConfig.edgePadding * 100).toFixed(0)}%</span>
            </div>
            <Slider
              id="edgePadding"
              value={[currentConfig.edgePadding]}
              onValueChange={(values) => handleSliderChange('edgePadding', values)}
              min={0}
              max={0.5}
              step={0.01}
              disabled={disabled}
            />
            <p className="text-xs text-muted-foreground">
              Minimum distance from video edges to prevent zoom area cutoff
            </p>
          </div>

          {/* Zoom Scale Range */}
          <div className="space-y-2">
            <Label>Zoom Scale Range</Label>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label htmlFor="minZoomScale" className="text-xs">Minimum</Label>
                <Input
                  id="minZoomScale"
                  type="number"
                  step="0.1"
                  min="1"
                  max={currentConfig.maxZoomScale}
                  value={currentConfig.minZoomScale}
                  onChange={(e) => handleConfigChange('minZoomScale', parseFloat(e.target.value))}
                  disabled={disabled}
                />
              </div>
              <div>
                <Label htmlFor="maxZoomScale" className="text-xs">Maximum</Label>
                <Input
                  id="maxZoomScale"
                  type="number"
                  step="0.1"
                  min={currentConfig.minZoomScale}
                  max="10"
                  value={currentConfig.maxZoomScale}
                  onChange={(e) => handleConfigChange('maxZoomScale', parseFloat(e.target.value))}
                  disabled={disabled}
                />
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Zoom scale range: {currentConfig.minZoomScale}x to {currentConfig.maxZoomScale}x
            </p>
          </div>

          {/* Preview/Status */}
          <div className="p-3 bg-green-50 rounded-md">
            <p className="text-sm font-medium text-green-800">✅ Cursor Following Active</p>
            <p className="text-xs text-green-600 mt-1">
              Zoom will automatically follow cursor movement with {(currentConfig.zoomRadius * 100).toFixed(0)}% radius 
              and {(currentConfig.smoothingFactor * 100).toFixed(0)}% smoothing.
            </p>
          </div>

          {/* Reset Button */}
          <Button
            variant="outline"
            onClick={resetToDefaults}
            disabled={disabled}
            className="w-full"
          >
            Reset to Global Defaults
          </Button>
        </CardContent>
      )}
      
      {!currentConfig.enabled && (
        <CardContent>
          <div className="p-4 bg-gray-50 rounded-md text-center">
            <p className="text-sm text-muted-foreground">
              Cursor following is disabled. Enable it to configure zoom behavior.
            </p>
          </div>
        </CardContent>
      )}
    </Card>
  );
};
